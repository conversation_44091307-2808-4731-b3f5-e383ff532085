from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import os
import sys
from typing import Optional
import httpx
import logging
from dotenv import load_dotenv

# Configure logging
logger = logging.getLogger(__name__)

# Add the current directory to the path so we can import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import UserInfo

# Load environment variables
load_dotenv()

# Get Django server URL from environment variables
DJANGO_SERVER_URL = os.getenv("DJANGO_SERVER_URL", "http://localhost:8000")

# Security scheme for API key authentication
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserInfo:
    """
    Validate the token by making a request to the Django server
    and return the authenticated user information

    Only accepts 'Bearer' prefix in the Authorization header.
    The 'Bearer' prefix will be stripped before validation.
    """
    try:
        # Get the raw token from credentials
        token = credentials.credentials

        # Log the token being sent (first few characters only for security)
        token_preview = token[:5] + "..." if len(token) > 5 else token
        logger.info(f"Sending token validation request with Token {token_preview}")

        # Make a request to Django server to validate the token
        async with httpx.AsyncClient() as client:
            # Set up request details
            url = f"{DJANGO_SERVER_URL}/api/users/auth/validate-token/"
            headers = {"Authorization": f"Token {token}"}
            logging.info(f"Using Token token format for authentication")

            # Log the request details
            logger.info(f"Making request to: {url}")

            # Make the request
            response = await client.get(url, headers=headers)

            if response.status_code != 200:
                error_text = await response.text()
                logger.error(f"Token validation failed with status {response.status_code}: {error_text}")

                # Try to parse the error response as JSON
                try:
                    import json
                    error_json = json.loads(error_text)
                    error_detail = error_json
                except:
                    error_detail = error_text[:100] if error_text else "No response body"

                # Provide more detailed error information
                detail = {
                    "message": "Invalid authentication token",
                    "status_code": response.status_code,
                    "django_response": error_detail,
                    "token_format": f"Token {token[:5]}..." if len(token) > 5 else f"Token {token}",
                    "django_server_url": DJANGO_SERVER_URL,
                    "validation_endpoint": f"{DJANGO_SERVER_URL}/api/users/auth/validate-token/"
                }

                # Log the full error details for debugging
                logger.error(f"Authentication error details: {detail}")

                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=detail,
                    headers={"WWW-Authenticate": "Token"},
                )

            user_data = response.json()
            # Create UserInfo with the token included
            return UserInfo(
                id=user_data["id"],
                username=user_data["username"],
                email=user_data.get("email"),
                token=token  # Store the original token for use in subsequent requests
            )

    except httpx.RequestError as e:
        print(f"Error in get_current_user: {str(e)}")
        logger.error(f"Authentication service unavailable: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Authentication service unavailable: {str(e)}",
            headers={"WWW-Authenticate": "Token"},
        )
    except Exception as e:
        print(f"Error in get_current_user: {str(e)}")
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Could not validate credentials: {str(e)}",
            headers={"WWW-Authenticate": "Token"},
        )