"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Clock, CheckCircle, XCircle, RotateCcw } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { quizApi, performanceApi } from "@/lib/api"
import { toast } from "@/hooks/use-toast"
import { calculateQuizScore, formatDuration, getPerformanceLevel } from "@/lib/quiz-utils"

interface QuizQuestion {
  question: string
  answer: string
}

interface QuizInterfaceProps {
  documentId?: number
}

export function QuizInterface({ documentId }: QuizInterfaceProps) {
  const [questions, setQuestions] = useState<QuizQuestion[]>([])
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [userAnswers, setUserAnswers] = useState<string[]>([])
  const [isQuizStarted, setIsQuizStarted] = useState(false)
  const [isQuizCompleted, setIsQuizCompleted] = useState(false)
  const [startTime, setStartTime] = useState<number>(0)
  const [timeElapsed, setTimeElapsed] = useState<number>(0)
  const [score, setScore] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [generating, setGenerating] = useState(false)

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isQuizStarted && !isQuizCompleted) {
      interval = setInterval(() => {
        setTimeElapsed(Date.now() - startTime)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isQuizStarted, isQuizCompleted, startTime])

  useEffect(() => {
    if (documentId) {
      loadQuiz()
    }
  }, [documentId])

  const formatTime = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const loadQuiz = async () => {
    if (!documentId) {
      toast({
        title: "Error",
        description: "No document selected for quiz",
        variant: "destructive",
      })
      return
    }

    setGenerating(true)
    try {
      const response = await quizApi.getOrGenerateQuiz(documentId)
      if (response.quizzes && response.quizzes.length > 0) {
        setQuestions(response.quizzes)
        setUserAnswers(new Array(response.quizzes.length).fill(''))

        if (response.generated) {
          toast({
            title: "Success",
            description: "Quiz generated successfully!",
          })
        } else {
          toast({
            title: "Quiz Loaded",
            description: "Existing quiz loaded successfully!",
          })
        }
      } else {
        throw new Error('No quiz data received')
      }
    } catch (error) {
      console.error('Error loading/generating quiz:', error)
      toast({
        title: "Error",
        description: "Failed to load or generate quiz. Please try again.",
        variant: "destructive",
      })
    } finally {
      setGenerating(false)
    }
  }

  const generateQuiz = async () => {
    if (!documentId) {
      toast({
        title: "Error",
        description: "No document selected for quiz generation",
        variant: "destructive",
      })
      return
    }

    setGenerating(true)
    try {
      const response = await quizApi.generateQuiz(documentId, 5)
      if (response.quizzes && response.quizzes.length > 0) {
        setQuestions(response.quizzes)
        setUserAnswers(new Array(response.quizzes.length).fill(''))
        toast({
          title: "Success",
          description: "New quiz generated successfully!",
        })
      } else {
        throw new Error('No quiz data received')
      }
    } catch (error) {
      console.error('Error generating quiz:', error)
      toast({
        title: "Error",
        description: "Failed to generate quiz. Please try again.",
        variant: "destructive",
      })
    } finally {
      setGenerating(false)
    }
  }

  const startQuiz = () => {
    setIsQuizStarted(true)
    setStartTime(Date.now())
    setTimeElapsed(0)
  }

  const handleAnswerChange = (answer: string) => {
    const newAnswers = [...userAnswers]
    newAnswers[currentQuestionIndex] = answer
    setUserAnswers(newAnswers)
  }

  const nextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1)
    }
  }

  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1)
    }
  }

  const calculateScore = () => {
    return calculateQuizScore(questions, userAnswers)
  }

  const submitQuiz = async () => {
    setLoading(true)
    const finalScore = calculateScore()
    const timeTakenSeconds = Math.floor(timeElapsed / 1000)
    
    setScore(finalScore)
    setIsQuizCompleted(true)

    // Save performance to backend
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}')
      if (user.id && documentId) {
        await performanceApi.createPerformance({
          student: user.id,
          document: documentId,
          quiz_score: finalScore,
          time_taken: timeTakenSeconds,
          remarks: `Quiz completed with ${Math.round(finalScore)}% score in ${formatTime(timeElapsed)}`
        })
        
        toast({
          title: "Quiz Completed!",
          description: `Your score: ${Math.round(finalScore)}% - Performance saved successfully`,
        })
      }
    } catch (error) {
      console.error('Error saving performance:', error)
      toast({
        title: "Quiz Completed!",
        description: `Your score: ${Math.round(finalScore)}% - Note: Performance could not be saved`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const resetQuiz = () => {
    setCurrentQuestionIndex(0)
    setUserAnswers(new Array(questions.length).fill(''))
    setIsQuizStarted(false)
    setIsQuizCompleted(false)
    setStartTime(0)
    setTimeElapsed(0)
    setScore(0)
  }

  if (questions.length === 0) {
    return (
      <div className="flex flex-col h-full">
        <ScrollArea className="flex-1">
          <div className="p-6 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Quiz</CardTitle>
                <CardDescription>
                  {!documentId
                    ? "Select a document to view or generate a quiz"
                    : generating
                      ? "Loading quiz questions..."
                      : "Generate a quiz based on your document content to test your understanding."
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {generating ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                  </div>
                ) : (
                  <>
                    <Button
                      onClick={loadQuiz}
                      disabled={!documentId}
                      className="w-full"
                      variant="outline"
                    >
                      Load Quiz
                    </Button>
                    <Button
                      onClick={generateQuiz}
                      disabled={!documentId}
                      className="w-full"
                    >
                      Generate New Quiz
                    </Button>
                  </>
                )}
                {!documentId && (
                  <p className="text-sm text-muted-foreground mt-2">
                    Please select a document to generate a quiz.
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </ScrollArea>
      </div>
    )
  }

  if (isQuizCompleted) {
    const performanceLevel = getPerformanceLevel(score)

    return (
      <div className="flex flex-col h-full">
        <ScrollArea className="flex-1">
          <div className="p-6 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  Quiz Completed!
                </CardTitle>
                <CardDescription className={performanceLevel.color}>
                  {performanceLevel.level} - {performanceLevel.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <p className={`text-2xl font-bold ${performanceLevel.color}`}>{Math.round(score)}%</p>
                    <p className="text-sm text-muted-foreground">Your Score</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{formatTime(timeElapsed)}</p>
                    <p className="text-sm text-muted-foreground">Time Taken</p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button onClick={resetQuiz} variant="outline" className="flex-1">
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Retake Quiz
                  </Button>
                  <Button onClick={generateQuiz} className="flex-1">
                    Generate New Quiz
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </ScrollArea>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-1">
        <div className="p-6 space-y-4">
          {/* Timer and Progress */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span className="font-mono">{formatTime(timeElapsed)}</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Question {currentQuestionIndex + 1} of {questions.length}
            </div>
          </div>

          <Progress value={((currentQuestionIndex + 1) / questions.length) * 100} />

          {/* Quiz Content */}
          {!isQuizStarted ? (
            <Card>
              <CardHeader>
                <CardTitle>Ready to Start?</CardTitle>
                <CardDescription>
                  You have {questions.length} questions to answer. Take your time and good luck!
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={startQuiz} className="w-full">
                  Start Quiz
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Question {currentQuestionIndex + 1}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-lg">{questions[currentQuestionIndex].question}</p>

                <Textarea
                  placeholder="Type your answer here..."
                  value={userAnswers[currentQuestionIndex]}
                  onChange={(e) => handleAnswerChange(e.target.value)}
                  className="min-h-[100px]"
                />

                <div className="flex justify-between">
                  <Button
                    onClick={previousQuestion}
                    disabled={currentQuestionIndex === 0}
                    variant="outline"
                  >
                    Previous
                  </Button>

                  {currentQuestionIndex === questions.length - 1 ? (
                    <Button
                      onClick={submitQuiz}
                      disabled={loading || !userAnswers[currentQuestionIndex].trim()}
                    >
                      {loading ? "Submitting..." : "Submit Quiz"}
                    </Button>
                  ) : (
                    <Button
                      onClick={nextQuestion}
                      disabled={!userAnswers[currentQuestionIndex].trim()}
                    >
                      Next
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
