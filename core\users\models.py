from django.contrib.auth.models import AbstractUser, Group, Permission
from django.db import models
from django.utils import timezone
from django.core.cache import cache
from datetime import timedelta
from django.db.models.signals import post_save
from django.dispatch import receiver

class Student(AbstractUser):
    is_paid = models.BooleanField(default=False)

    email = models.EmailField(unique=True)
    is_email_verified = models.BooleanField(default=False)
    email_verification_token = models.Char<PERSON>ield(max_length=100, null=True, blank=True)
    email_verification_sent_at = models.DateTimeField(null=True, blank=True)
    otp = models.Char<PERSON>ield(max_length=6, null=True, blank=True)
    otp_created_at = models.DateTimeField(null=True, blank=True)

    USERNAME_FIELD = 'email'  # Login using email instead of username
    REQUIRED_FIELDS = ['username']  # username is still required in form

    groups = models.ManyToManyField(
        Group,
        related_name='student_users',
        blank=True,
        help_text='The groups this user belongs to.',
        verbose_name='groups'
    )
    user_permissions = models.ManyToManyField(
        Permission,
        related_name='student_users',
        blank=True,
        help_text='Specific permissions for this user.',
        verbose_name='user permissions'
    )

    def __str__(self):
        return self.email

    def generate_verification_token(self):
        import secrets
        self.email_verification_token = secrets.token_urlsafe(32)
        self.email_verification_sent_at = timezone.now()
        self.save()
        return self.email_verification_token

    def generate_otp(self):
        import random
        import string
        self.otp = ''.join(random.choices(string.digits, k=6))
        self.otp_created_at = timezone.now()
        self.save()
        return self.otp

    def verify_otp(self, otp):
        if not self.otp or not self.otp_created_at:
            return False

        # Check if OTP is expired (10 minutes validity)
        if timezone.now() > self.otp_created_at + timezone.timedelta(minutes=10):
            return False

        return self.otp == otp

@receiver(post_save, sender=Student)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=Student)
def save_user_profile(sender, instance, **kwargs):
    if not hasattr(instance, 'userprofile'):
        UserProfile.objects.create(user=instance)
    instance.userprofile.save()

class UserProfile(models.Model):
    user = models.OneToOneField('Student', on_delete=models.CASCADE)
    is_paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {'Paid' if self.is_paid else 'Free'}"

class UserUsage(models.Model):
    user = models.ForeignKey('Student', on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    chat_count = models.IntegerField(default=0)
    file_upload_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'date')
        indexes = [
            models.Index(fields=['user', 'date']),
            models.Index(fields=['date']),  # For efficient cleanup
        ]

    def __str__(self):
        return f"{self.user.username} - {self.date} - Chats: {self.chat_count}, Files: {self.file_upload_count}"

    @property
    def chat_limit(self):
        return 100 if self.user.userprofile.is_paid else 5000

    @property
    def file_upload_limit(self):
        return 5 if self.user.userprofile.is_paid else 100

    def can_make_chat(self):
        return self.chat_count < self.chat_limit

    def can_upload_file(self):
        return self.file_upload_count < self.file_upload_limit

    def increment_chat_count(self):
        self.chat_count = self.chat_count + 1
        UserUsage.objects.filter(id=self.id).update(chat_count=self.chat_count)
        self._update_cache()

    def increment_file_upload_count(self):
        self.file_upload_count = self.file_upload_count + 1
        UserUsage.objects.filter(id=self.id).update(file_upload_count=self.file_upload_count)
        self._update_cache()

    def _update_cache(self):
        """Update cache with current usage data"""
        cache_key = f"user_usage_{self.user.id}_{self.date}"
        cache.set(cache_key, {
            'chat_count': self.chat_count,
            'file_upload_count': self.file_upload_count,
            'chat_limit': self.chat_limit,
            'file_upload_limit': self.file_upload_limit
        }, timeout=86400)  # Cache for 24 hours

    @classmethod
    def get_or_create_usage(cls, user, date=None):
        """Get or create usage record with caching"""
        if date is None:
            date = timezone.now().date()

        cache_key = f"user_usage_{user.id}_{date}"
        cached_data = cache.get(cache_key)

        if cached_data:
            usage, _ = cls.objects.get_or_create(
                user=user,
                date=date,
                defaults={
                    'chat_count': cached_data['chat_count'],
                    'file_upload_count': cached_data['file_upload_count']
                }
            )
            return usage

        usage, created = cls.objects.get_or_create(
            user=user,
            date=date,
            defaults={'chat_count': 0, 'file_upload_count': 0}
        )

        if created:
            usage._update_cache()

        return usage

    @classmethod
    def cleanup_old_records(cls, days_to_keep=30):
        """Efficient cleanup of old records using bulk operations"""
        cutoff_date = timezone.now().date() - timedelta(days=days_to_keep)

        # Get IDs of records to delete
        old_record_ids = list(cls.objects.filter(date__lt=cutoff_date).values_list('id', flat=True))

        if old_record_ids:
            # Delete in chunks to avoid memory issues
            chunk_size = 1000
            for i in range(0, len(old_record_ids), chunk_size):
                chunk = old_record_ids[i:i + chunk_size]
                cls.objects.filter(id__in=chunk).delete()

        return len(old_record_ids)


class StudentPerformance(models.Model):
    """
    Model to track student performance on quizzes for specific documents.
    Each entry represents a single quiz attempt with score and time taken.
    """
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='performances')
    document = models.ForeignKey('documents.Document', on_delete=models.CASCADE, related_name='student_performances')
    quiz_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                    help_text="Score achieved on the quiz (percentage)")
    time_taken = models.PositiveIntegerField(help_text="Time taken to complete the quiz (in seconds)")
    remarks = models.TextField(blank=True, null=True, help_text="Feedback or comments on student performance")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['student', 'document']),
            models.Index(fields=['quiz_score']),  # For performance analytics
            models.Index(fields=['created_at']),  # For chronological ordering
        ]
        ordering = ['-created_at']
        verbose_name = "Student Performance"
        verbose_name_plural = "Student Performances"

    def __str__(self):
        return f"{self.student.username} - {self.document.title} - Score: {self.quiz_score}% - {self.created_at.strftime('%Y-%m-%d %H:%M')}"
