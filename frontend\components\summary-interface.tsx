"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, CheckCircle, FileText, Sparkles } from "lucide-react"
import { toast } from "sonner"
import { documentApi } from "@/lib/api"

interface SummaryInterfaceProps {
  documentId?: number
}

export function SummaryInterface({ documentId }: SummaryInterfaceProps) {
  const [summary, setSummary] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    if (documentId) {
      loadSummary()
    }
  }, [documentId])

  const loadSummary = async () => {
    if (!documentId) {
      setError("No document selected")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // For now, we'll simulate the summary generation
      // In a real implementation, you would call an API endpoint
      await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API call
      
      const mockSummary = `This document provides a comprehensive overview of machine learning fundamentals and advanced concepts. 

Key topics covered include:

• **Supervised Learning**: Classification and regression algorithms including linear regression, decision trees, random forests, and support vector machines.

• **Unsupervised Learning**: Clustering techniques such as K-means, hierarchical clustering, and dimensionality reduction methods like PCA and t-SNE.

• **Deep Learning**: Neural network architectures, backpropagation, convolutional neural networks (CNNs), and recurrent neural networks (RNNs).

• **Model Evaluation**: Cross-validation, performance metrics, overfitting prevention, and hyperparameter tuning strategies.

• **Feature Engineering**: Data preprocessing, feature selection, normalization, and handling missing data.

• **Real-world Applications**: Case studies in computer vision, natural language processing, and recommendation systems.

The document emphasizes practical implementation using Python libraries such as scikit-learn, TensorFlow, and PyTorch. It includes code examples, best practices, and common pitfalls to avoid when building machine learning models.

This comprehensive guide serves as both an introduction for beginners and a reference for experienced practitioners looking to deepen their understanding of machine learning concepts and applications.`

      setSummary(mockSummary)
    } catch (err) {
      console.error('Error loading summary:', err)
      setError('Failed to load summary. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const generateNewSummary = async () => {
    if (!documentId) {
      toast.error("No document selected for summary generation")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Simulate new summary generation
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      const newSummary = `Updated Summary: This document explores advanced machine learning techniques with a focus on practical applications and industry best practices.

**Core Concepts:**

• **Algorithm Selection**: Comprehensive comparison of different ML algorithms and their use cases, including when to use linear models vs. tree-based methods vs. neural networks.

• **Data Pipeline Design**: End-to-end workflow from data collection and cleaning to model deployment and monitoring.

• **Performance Optimization**: Techniques for improving model accuracy, reducing training time, and optimizing inference speed.

• **Ethical AI**: Considerations for bias detection, fairness metrics, and responsible AI development practices.

**Advanced Topics:**

• **Ensemble Methods**: Bagging, boosting, and stacking techniques for improved model performance.

• **Transfer Learning**: Leveraging pre-trained models and fine-tuning strategies for domain-specific applications.

• **AutoML**: Automated machine learning tools and techniques for streamlining the model development process.

• **MLOps**: Best practices for model versioning, continuous integration, and production deployment.

The document provides practical insights backed by real-world examples and case studies from various industries including healthcare, finance, and technology. It serves as a comprehensive guide for data scientists and ML engineers looking to implement robust, scalable machine learning solutions.`

      setSummary(newSummary)
      toast.success("New summary generated successfully!")
    } catch (err) {
      console.error('Error generating summary:', err)
      setError('Failed to generate new summary. Please try again.')
      toast.error("Failed to generate new summary")
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = async () => {
    if (!summary) return

    try {
      await navigator.clipboard.writeText(summary)
      setCopied(true)
      toast.success("Summary copied to clipboard!")
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
      toast.error("Failed to copy summary")
    }
  }

  if (!documentId) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Summary
            </CardTitle>
            <CardDescription>
              Select a document to view or generate its summary
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Please select a document to generate a summary.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Summary
            </CardTitle>
            <CardDescription>
              {summary ? "Generating new summary..." : "Loading summary..."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Summary
            </CardTitle>
            <CardDescription className="text-red-500">
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={loadSummary} className="w-full">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Summary
          </CardTitle>
          <CardDescription>
            AI-generated summary of your document content
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <div className="whitespace-pre-wrap text-sm leading-relaxed">
              {summary}
            </div>
          </div>
          
          <div className="flex gap-2 pt-4 border-t">
            <Button
              onClick={copyToClipboard}
              variant="outline"
              className="flex-1"
              disabled={!summary}
            >
              {copied ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Summary
                </>
              )}
            </Button>
            <Button
              onClick={generateNewSummary}
              className="flex-1"
              disabled={isLoading}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Generate New Summary
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
